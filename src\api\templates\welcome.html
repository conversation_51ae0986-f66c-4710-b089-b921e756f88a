<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!-- =====BOX ICONS===== -->
        <link href='https://cdn.jsdelivr.net/npm/boxicons@2.0.5/css/boxicons.min.css' rel='stylesheet'>

        <!-- ===== CSS ===== -->
        <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
        <!-- Google Fonts -->
        <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@500;600&display=swap" rel="stylesheet">
        <!-- Optional: Animate.css for card reveal -->
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css">

        <title><PERSON><PERSON></title>

        <style>
            /* About Cards Styles */
            .about__cards {
                display: grid;
                grid-template-columns: 1fr;
                gap: 1.5rem;
                margin-top: 2rem;
            }

            .card {
                border: none;
                border-radius: 1rem;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
                background-color: #fff;
                padding: 1.5rem;
                transition: transform 0.3s ease;
            }

            .card:hover {
                transform: translateY(-5px);
            }

            .card__title {
                color: #222;
                font-weight: 600;
                margin-bottom: 1rem;
                font-size: 1.2rem;
            }

            .card__text {
                color: #444;
                font-size: 0.95rem;
            }

            /* Tips Section Styles */
            .tips {
                padding: 4rem 0;
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            }

            .tips__container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 0 1rem;
            }

            .tips__content {
                text-align: center;
            }

            .tips__subtitle {
                color: #666;
                font-size: 1.1rem;
                margin-bottom: 2rem;
                font-weight: 400;
            }

            .tips__card {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 2rem;
                max-width: 800px;
                margin: 0 auto;
                padding: 2rem;
                background: white;
                border-radius: 2rem;
                box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
                transition: transform 0.3s ease;
            }

            .tips__card:hover {
                transform: translateY(-5px);
            }

            .tips__character {
                flex-shrink: 0;
            }

            .tips__img {
                width: 150px;
                height: 150px;
                object-fit: contain;
                animation: bounce 2s infinite;
            }

            .tips__bubble {
                flex: 1;
                position: relative;
                background: var(--first-color);
                padding: 1.5rem;
                border-radius: 1.5rem;
                border: 3px solid var(--first-color-dark);
            }

            .tips__bubble::before {
                content: '';
                position: absolute;
                left: -15px;
                top: 50%;
                transform: translateY(-50%);
                width: 0;
                height: 0;
                border-top: 15px solid transparent;
                border-bottom: 15px solid transparent;
                border-right: 15px solid var(--first-color-dark);
            }

            .tips__text {
                font-size: 1.1rem;
                color: #333;
                line-height: 1.6;
                margin-bottom: 1rem;
                font-weight: 500;
                transition: opacity 0.3s ease;
                min-height: 3em;
                display: flex;
                align-items: center;
            }

            .tips__button {
                background: var(--first-color-dark);
                color: #333;
                border: none;
                padding: 0.5rem 1rem;
                border-radius: 25px;
                cursor: pointer;
                font-size: 0.9rem;
                font-weight: 600;
                transition: all 0.3s ease;
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
            }

            .tips__button:hover {
                background: var(--first-color-darken);
                transform: scale(1.05);
            }

            @keyframes bounce {
                0%, 20%, 50%, 80%, 100% {
                    transform: translateY(0);
                }
                40% {
                    transform: translateY(-10px);
                }
                60% {
                    transform: translateY(-5px);
                }
            }

            /* Footer Styles */
            .footer {
                padding: 3rem 0 1rem;
                text-align: center;
                background-color: var(--first-color);
            }

            .footer__container {
                row-gap: 2rem;
            }

            .footer__copy {
                font-size: 0.875rem;
                color: #666;
            }

            @media screen and (min-width: 768px) {
                .about__cards {
                    grid-template-columns: repeat(2, 1fr);
                }

                .footer {
                    padding: 4rem 0 2rem;
                }

                .tips__card {
                    gap: 3rem;
                    padding: 3rem;
                }

                .tips__img {
                    width: 200px;
                    height: 200px;
                }
            }

            @media screen and (max-width: 768px) {
                .tips__card {
                    flex-direction: column;
                    text-align: center;
                    gap: 1.5rem;
                }

                .tips__bubble::before {
                    display: none;
                }

                .tips__img {
                    width: 120px;
                    height: 120px;
                }
            }
        </style>
    </head>
    <body>
        <!--===== HEADER =====-->
        <header class="l-header">
            <nav class="nav bd-grid">
                <div>
                   <br><br> <a href="#" class="nav__logo"><img src="{{ url_for('static', filename='images/logo.png') }}" alt="Logo" width="120" height="auto"></a>

                </div>

                <div class="nav__toggle" id="nav-toggle">
                    <i class='bx bx-menu'></i>
                </div>

                <div class="nav__menu" id="nav-menu">
                    <div class="nav__close" id="nav-close">
                        <i class='bx bx-x'></i>
                    </div>

                    <ul class="nav__list">
                        <li class="nav__item"><a href="#home" class="nav__link active">Home</a></li>
                        <li class="nav__item"><a href="#about" class="nav__link">About</a></li>
                        <li class="nav__item"><a href="/login" class="nav__link">Login</a></li>
                        <li class="nav__item"><a href="#contact" class="nav__link">Contact</a></li>
                    </ul>
                </div>
            </nav>
        </header>

        <main class="l-main">
            <!--===== HOME =====-->
            <section class="home" id="home">
                <div class="home__container bd-grid">
                    <div class="home__img">
                        <img src="{{ url_for('static', filename='images/imgA.png') }}" alt="" data-speed="-2" class="move">
                        <img src="{{ url_for('static', filename='images/imgB.png') }}" alt="" data-speed="2" class="move">
                        <img src="{{ url_for('static', filename='images/imgC.png') }}" alt="" data-speed="2" class="move">
                        <img src="{{ url_for('static', filename='images/imgD.png') }}" alt="" data-speed="-2" class="move">
                        <img src="{{ url_for('static', filename='images/imgE.png') }}" alt="" data-speed="-2" class="move">
                        <img src="{{ url_for('static', filename='images/imgF.png') }}" alt="" data-speed="2" class="move">
                    </div>

                    <div class="home__data">
                        <h1 class="home__title">SISA<br> RASA</h1>
                        <p class="home__description">Rasa baru dari Sisa Lama. </p>
                        <a href="/login" class="home__button">Get Started</a>
                    </div>
                </div>
            </section>

            <!--===== ABOUT =====-->
            <section class="about" id="about">
                <div class="about__container bd-grid">
                    <div class="about__data">
                        <h2 class="section__title">About SisaRasa</h2>
                        <p class="about__description">
                            Rasa baru dari sisa lama. At Sisa Rasa, we believe that every ingredient has potential. By combining smart technology with creative cooking, we're changing how people think about food waste. Our goal ? To make eating easier, smarter, and more delicious. 
                            Whether you're a seasoned chef or simply aiming to use up what's in your fridge, Sisa Rasa is your partner in creating delicious meals.
                        </p>

                        <!-- About Cards Section -->
                        <div class="about__cards">
                            <div class="card">
                                <div class="card__body">
                                    <h5 class="card__title">🌱 Our Missions </h5>
                                    <p class="card__text">
                                        Our mission is to encourage households to utilise the most of their leftover ingredients rather than letting them go to waste. We help consumers in transforming their pantry or refrigerator leftovers into mouthwatering dinners with the support of our intelligent recipe recommendation system. 
                                        <br> <br>By doing this, we hope to not only reduce food waste at home but also inspire small, meaningful steps toward a more sustainable future.
                                    </p>
                                </div>
                            </div>
                            <div class="card">
                                <div class="card__body">
                                    <h5 class="card__title">👨‍🍳 What is Sisa Rasa?</h5>
                                    <p class="card__text">
                                        Sisa Rasa is an AI-powered web application that helps users discover recipes based on the leftover ingredients they already have. Using the K-Nearest Neighbors (KNN) algorithm, the system analyzes your input and recommends the closest matching recipes,maximizing usage and minimizing waste.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!--===== FOOD WASTE TIPS =====-->
            <section class="tips" id="tips">
                <div class="tips__container bd-grid">
                    <div class="tips__content">
                        <h2 class="section__title">Food Waste Reduction Tips</h2>
                        <p class="tips__subtitle">Mr. Bob shares amazing facts to reduce food waste at home!</p>

                        <div class="tips__card">
                            <div class="tips__character">
                                <img src="{{ url_for('static', filename='images/mrTips.png') }}" alt="Mr. Tips" class="tips__img">
                            </div>
                            <div class="tips__bubble">
                                <div class="tips__text" id="tipText">
                                    <!-- Random tip will be loaded here -->
                                </div>
                                <div class="tips__refresh">
                                    <button class="tips__button" onclick="getNewTip()">
                                        <i class='bx bx-refresh'></i> New Tip
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!--===== CONTACT =====-->
            <section class="contact" id="contact">
                <div class="contact__container bd-grid">
                    <div class="contact__data">
                        <h2 class="section__title">Contact Us</h2>
                        <p class="contact__description">Have questions about our recipe recommendation system? Feel free to reach out to us!</p>
                        <div class="contact__info">
                            <div class="contact__item">
                                <i class='bx bx-envelope'></i>
                                <p><EMAIL></p>
                            </div>
                            <div class="contact__item">
                                <i class='bx bx-phone'></i>
                                <p>01135723003</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!--===== FOOTER =====-->
            <footer class="footer">
                <div class="footer__container bd-grid">
                    <p class="footer__copy">© 2025 Sisa Rasa. All rights reserved.</p>
                </div>
            </footer>
        </main>

        <!--===== GSAP =====-->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.5.1/gsap.min.js"></script>

        <!--===== MAIN JS =====-->
        <script src="{{ url_for('static', filename='main.js') }}"></script>

        <!-- Optional: Confetti JS -->
        <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js"></script>

        <!-- Card Animation Script -->
        <script>
            // === Food Waste Tips Data ===
            const foodWasteTips = [
                "💡 Did you know? Every year, 1.3 billion tons of food is wasted worldwide!",
                "🥬 Store leafy greens in airtight containers with damp paper towels to keep them fresh longer.",
                "🍌 Overripe bananas? Use them to make delicious smoothies or banana bread!",
                "❄️ Freeze fresh herbs in ice cubes with olive oil for future cooking use.",
                "🍞 Stale bread can be turned into breadcrumbs or tasty French toast!",
                "🥕 Potato and carrot peels can be roasted into healthy, crispy chips.",
                "🍅 Soft tomatoes are still perfect for sauces, soups, or stir-fry dishes.",
                "🧅 Store onions in a cool, dry place - not in the refrigerator!",
                "🥛 Milk about to expire? Use it to make pancakes, cakes, or smoothies.",
                "🍋 Squeeze lemon juice and freeze it in ice trays for long-term use.",
                "🥒 Wilted cucumbers can become natural face masks or infused water!",
                "🌿 Regrow vegetable stems like green onions by placing them in water.",
                "🍚 Leftover rice? Try making fried rice, porridge, or even rice pudding dessert!",
                "🥗 Blend almost-spoiled vegetables into nutritious green smoothies.",
                "📅 Use the 'first in, first out' system - use older food before newer items.",
                "🥖 Day-old bread makes excellent croutons when toasted with herbs and oil.",
                "🍎 Brown apple slices are perfect for baking pies, muffins, or applesauce.",
                "🥔 Sprouting potatoes? Remove sprouts and eyes, then cook normally - they're still safe!",
                "🧄 Garlic cloves starting to sprout can still be used - just remove the green center.",
                "🥦 Broccoli stems are edible! Peel and chop them for stir-fries or soups."
            ];

            let currentTipIndex = 0;

            // === Get Random Tip Function ===
            function getNewTip() {
                const tipText = document.getElementById('tipText');
                const randomIndex = Math.floor(Math.random() * foodWasteTips.length);

                // Add fade out effect
                tipText.style.opacity = '0';

                setTimeout(() => {
                    tipText.textContent = foodWasteTips[randomIndex];
                    tipText.style.opacity = '1';
                    currentTipIndex = randomIndex;
                }, 300);
            }

            // === Initialize on page load ===
            document.addEventListener('DOMContentLoaded', function() {
                // Load initial tip
                getNewTip();

                // === Scroll fade-in for cards ===
                const cards = document.querySelectorAll('.card, .tips__card');

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('animate__animated', 'animate__fadeInUp');
                            observer.unobserve(entry.target);
                        }
                    });
                }, {
                    threshold: 0.2
                });

                cards.forEach(card => {
                    observer.observe(card);
                });

                // === Fun: Confetti on heading click ===
                const sectionTitles = document.querySelectorAll('.section__title');
                sectionTitles.forEach(title => {
                    title.addEventListener('click', () => {
                        confetti();
                    });
                });

                // === Auto-refresh tip every 10 seconds ===
                setInterval(getNewTip, 10000);
            });
        </script>
    </body>
</html>